{% extends 'Hr/base.html' %}
{% load static %}

{% block title %}سجلات الحضور والانصراف - نظام الموارد البشرية - ElDawliya{% endblock %}

{% block page_title %}
    <i class="fas fa-clock me-2"></i>
    سجلات الحضور والانصراف
{% endblock %}

{% block header_actions %}
    <div class="btn-group" role="group">
        <a href="{% url 'hr:new_attendance_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            تسجيل حضور يدوي
        </a>
        <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#quickAttendanceModal">
            <i class="fas fa-bolt"></i>
            تسجيل سريع
        </button>
        <a href="{% url 'hr:new_attendance_report' %}" class="btn btn-outline-info">
            <i class="fas fa-chart-line"></i>
            التقارير
        </a>
    </div>
{% endblock %}

{% block content %}
<!-- Today's Statistics -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card border-start border-success border-4">
            <div class="stats-number text-success">{{ today_stats.present_employees }}</div>
            <div class="stats-label">حاضر اليوم</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card border-start border-warning border-4">
            <div class="stats-number text-warning">{{ today_stats.late_employees }}</div>
            <div class="stats-label">متأخر</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card border-start border-info border-4">
            <div class="stats-number text-info">{{ today_stats.overtime_employees }}</div>
            <div class="stats-label">وقت إضافي</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card border-start border-primary border-4">
            <div class="stats-number text-primary">{{ today_stats.total_records }}</div>
            <div class="stats-label">إجمالي السجلات</div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            البحث والتصفية
        </h5>
    </div>
    <div class="card-body">
        <form method="get" id="filterForm">
            <div class="row g-3">
                <!-- Date Range -->
                <div class="col-lg-3 col-md-6">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>
                <div class="col-lg-3 col-md-6">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>

                <!-- Employee Filter -->
                <div class="col-lg-3 col-md-6">
                    <label for="employee" class="form-label">الموظف</label>
                    <select class="form-select" id="employee" name="employee">
                        <option value="">جميع الموظفين</option>
                        {% for emp in employees %}
                            <option value="{{ emp.id }}" {% if employee_filter == emp.id|stringformat:"s" %}selected{% endif %}>
                                {{ emp.full_name }} ({{ emp.employee_number }})
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Status Filter -->
                <div class="col-lg-3 col-md-6">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Attendance Type Filter -->
                <div class="col-lg-3 col-md-6">
                    <label for="attendance_type" class="form-label">نوع الحضور</label>
                    <select class="form-select" id="attendance_type" name="attendance_type">
                        <option value="">جميع الأنواع</option>
                        {% for value, label in attendance_type_choices %}
                            <option value="{{ value }}" {% if attendance_type_filter == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                        <i class="fas fa-eraser"></i>
                        مسح الفلاتر
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="setTodayFilter()">
                        <i class="fas fa-calendar-day"></i>
                        اليوم
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="setWeekFilter()">
                        <i class="fas fa-calendar-week"></i>
                        هذا الأسبوع
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Attendance Records Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            سجلات الحضور ({{ attendance_records|length }} من {{ page_obj.paginator.count }})
        </h5>
        <div class="btn-group btn-group-sm">
            <button type="button" class="btn btn-outline-primary" onclick="refreshData()">
                <i class="fas fa-sync-alt"></i>
                تحديث
            </button>
            <button type="button" class="btn btn-outline-success" onclick="exportData()">
                <i class="fas fa-file-excel"></i>
                تصدير
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        {% if attendance_records %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>التاريخ</th>
                            <th>الوقت</th>
                            <th>نوع الحضور</th>
                            <th>الحالة</th>
                            <th>دقائق التأخير</th>
                            <th>الوقت الإضافي</th>
                            <th>الجهاز</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in attendance_records %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if record.employee.photo %}
                                        <img src="{{ record.employee.photo.url }}" class="rounded-circle me-2" width="32" height="32">
                                    {% else %}
                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" 
                                             style="width: 32px; height: 32px;">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    {% endif %}
                                    <div>
                                        <strong>{{ record.employee.full_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ record.employee.employee_number }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <strong>{{ record.date }}</strong>
                                <br>
                                <small class="text-muted">{{ record.date|date:"l" }}</small>
                            </td>
                            <td>
                                <strong>{{ record.timestamp|time:"H:i" }}</strong>
                                <br>
                                <small class="text-muted">{{ record.timestamp|time:"A" }}</small>
                            </td>
                            <td>
                                <span class="badge bg-{% if record.attendance_type == 'check_in' %}success{% else %}info{% endif %}">
                                    {{ record.get_attendance_type_display }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{% if record.status == 'present' %}success{% elif record.status == 'late' %}warning{% elif record.status == 'overtime' %}info{% else %}secondary{% endif %}">
                                    {{ record.get_status_display }}
                                </span>
                            </td>
                            <td>
                                {% if record.late_minutes %}
                                    <span class="text-warning fw-bold">{{ record.late_minutes }} دقيقة</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if record.overtime_minutes %}
                                    <span class="text-info fw-bold">{{ record.overtime_minutes }} دقيقة</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if record.machine %}
                                    <small class="text-muted">{{ record.machine.name }}</small>
                                {% elif record.is_manual %}
                                    <span class="badge bg-secondary">يدوي</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-info" 
                                            onclick="showRecordDetails({{ record.id }})" title="التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {% if record.is_manual %}
                                    <button type="button" class="btn btn-outline-warning" 
                                            onclick="editRecord({{ record.id }})" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="deleteRecord({{ record.id }})" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <div class="card-footer">
                <nav aria-label="تنقل الصفحات">
                    <ul class="pagination justify-content-center mb-0">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page=1">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.paginator.num_pages }}">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد سجلات حضور</h5>
                <p class="text-muted">لم يتم العثور على سجلات حضور مطابقة لمعايير البحث</p>
                <a href="{% url 'hr:new_attendance_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    تسجيل أول حضور
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Quick Attendance Modal -->
<div class="modal fade" id="quickAttendanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-bolt me-2"></i>
                    تسجيل حضور سريع
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="quickAttendanceForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="quickEmployee" class="form-label">الموظف</label>
                        <select class="form-select" id="quickEmployee" name="employee_id" required>
                            <option value="">-- اختر الموظف --</option>
                            {% for emp in employees %}
                                <option value="{{ emp.id }}">{{ emp.full_name }} ({{ emp.employee_number }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="quickAttendanceType" class="form-label">نوع الحضور</label>
                        <select class="form-select" id="quickAttendanceType" name="attendance_type" required>
                            <option value="">-- اختر النوع --</option>
                            <option value="check_in">دخول</option>
                            <option value="check_out">خروج</option>
                        </select>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم تسجيل الوقت الحالي تلقائياً
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitQuickAttendance()">
                    <i class="fas fa-save"></i>
                    تسجيل
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Record Details Modal -->
<div class="modal fade" id="recordDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye me-2"></i>
                    تفاصيل سجل الحضور
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="recordDetailsContent">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Filter functions
function clearFilters() {
    $('#filterForm')[0].reset();
    window.location.href = '{% url "hr:new_attendance_list" %}';
}

function setTodayFilter() {
    const today = new Date().toISOString().split('T')[0];
    $('#date_from').val(today);
    $('#date_to').val(today);
    $('#filterForm').submit();
}

function setWeekFilter() {
    const today = new Date();
    const firstDay = new Date(today.setDate(today.getDate() - today.getDay()));
    const lastDay = new Date(today.setDate(today.getDate() - today.getDay() + 6));
    
    $('#date_from').val(firstDay.toISOString().split('T')[0]);
    $('#date_to').val(lastDay.toISOString().split('T')[0]);
    $('#filterForm').submit();
}

// Quick attendance
function submitQuickAttendance() {
    const formData = new FormData($('#quickAttendanceForm')[0]);
    
    $.post('{% url "hr:new_attendance_quick_add" %}', formData, function(response) {
        if (response.success) {
            $('#quickAttendanceModal').modal('hide');
            showAlert(response.message, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert(response.error, 'danger');
        }
    }).fail(function() {
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
}

// Record details
function showRecordDetails(recordId) {
    $('#recordDetailsModal').modal('show');
    
    // Load record details via AJAX
    $.get(`/hr/attendance/record/${recordId}/details/`, function(data) {
        $('#recordDetailsContent').html(data);
    }).fail(function() {
        $('#recordDetailsContent').html('<div class="alert alert-danger">حدث خطأ في تحميل البيانات</div>');
    });
}

// Edit record
function editRecord(recordId) {
    window.location.href = `/hr/attendance/record/${recordId}/edit/`;
}

// Delete record
function deleteRecord(recordId) {
    if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
        $.post(`/hr/attendance/record/${recordId}/delete/`, {
            csrfmiddlewaretoken: '{{ csrf_token }}'
        }, function(response) {
            if (response.success) {
                showAlert('تم حذف السجل بنجاح', 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('حدث خطأ في حذف السجل', 'danger');
            }
        });
    }
}

// Refresh data
function refreshData() {
    location.reload();
}

// Export data
function exportData() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.open(`${window.location.pathname}?${params.toString()}`);
}

// Show alert
function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.main-content').prepend(alertHtml);
    
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

// Auto-refresh every 5 minutes
setInterval(function() {
    if (document.visibilityState === 'visible') {
        refreshData();
    }
}, 300000);
</script>
{% endblock %}
