{% extends 'Hr/base.html' %}
{% load static %}

{% block title %}{{ employee.full_name }} - تفاصيل الموظف - ElDawliya{% endblock %}

{% block page_title %}
    <i class="fas fa-user me-2"></i>
    ملف الموظف - {{ employee.full_name }}
{% endblock %}

{% block header_actions %}
    <div class="btn-group" role="group">
        <a href="{% url 'hr:new_employee_update' employee.pk %}" class="btn btn-warning">
            <i class="fas fa-edit"></i>
            تعديل البيانات
        </a>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-plus"></i>
                إضافة
            </button>
            <ul class="dropdown-menu">
                <li>
                    <a class="dropdown-item" href="{% url 'hr:new_employee_document_create' employee.pk %}">
                        <i class="fas fa-file-upload me-2"></i>
                        رفع وثيقة
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="{% url 'hr:new_employee_emergency_contact_create' employee.pk %}">
                        <i class="fas fa-phone me-2"></i>
                        جهة اتصال طوارئ
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="#">
                        <i class="fas fa-calendar-plus me-2"></i>
                        طلب إجازة
                    </a>
                </li>
            </ul>
        </div>
        <a href="{% url 'hr:new_employee_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i>
            العودة للقائمة
        </a>
    </div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Employee Profile Card -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-body text-center">
                <!-- Employee Photo -->
                <div class="mb-3">
                    {% if employee.photo %}
                        <img src="{{ employee.photo.url }}" class="rounded-circle border border-3 border-primary" 
                             width="120" height="120" alt="{{ employee.full_name }}">
                    {% else %}
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto border border-3 border-primary" 
                             style="width: 120px; height: 120px;">
                            <i class="fas fa-user fa-3x text-white"></i>
                        </div>
                    {% endif %}
                </div>

                <!-- Employee Basic Info -->
                <h4 class="mb-1">{{ employee.full_name }}</h4>
                <p class="text-muted mb-2">{{ employee.job_position.title|default:"غير محدد" }}</p>
                <p class="text-muted mb-3">{{ employee.department.name|default:"غير محدد" }}</p>

                <!-- Status Badge -->
                <span class="badge bg-{% if employee.status == 'active' %}success{% elif employee.status == 'inactive' %}secondary{% elif employee.status == 'on_leave' %}warning{% else %}danger{% endif %} fs-6 mb-3">
                    {{ employee.get_status_display }}
                </span>

                <!-- Quick Actions -->
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary" onclick="updateEmployeeStatus()">
                        <i class="fas fa-toggle-on"></i>
                        تغيير الحالة
                    </button>
                    <a href="mailto:{{ employee.email }}" class="btn btn-outline-info">
                        <i class="fas fa-envelope"></i>
                        إرسال بريد إلكتروني
                    </a>
                    <a href="tel:{{ employee.phone_primary }}" class="btn btn-outline-success">
                        <i class="fas fa-phone"></i>
                        اتصال
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Stats Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h5 class="text-primary mb-0">{{ age }}</h5>
                            <small class="text-muted">العمر</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h5 class="text-success mb-0">{{ years_of_service }}</h5>
                        <small class="text-muted">سنوات الخدمة</small>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <h5 class="text-info mb-0">{{ documents.count }}</h5>
                            <small class="text-muted">الوثائق</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h5 class="text-warning mb-0">{{ recent_attendance }}</h5>
                        <small class="text-muted">أيام الحضور</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Employee Details -->
    <div class="col-lg-8">
        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs" id="employeeTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="personal-tab" data-bs-toggle="tab" data-bs-target="#personal" 
                        type="button" role="tab">
                    <i class="fas fa-user me-2"></i>
                    البيانات الشخصية
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="employment-tab" data-bs-toggle="tab" data-bs-target="#employment" 
                        type="button" role="tab">
                    <i class="fas fa-briefcase me-2"></i>
                    بيانات التوظيف
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="documents-tab" data-bs-toggle="tab" data-bs-target="#documents" 
                        type="button" role="tab">
                    <i class="fas fa-file-alt me-2"></i>
                    الوثائق ({{ documents.count }})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="contacts-tab" data-bs-toggle="tab" data-bs-target="#contacts" 
                        type="button" role="tab">
                    <i class="fas fa-phone me-2"></i>
                    جهات الاتصال
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="attendance-tab" data-bs-toggle="tab" data-bs-target="#attendance" 
                        type="button" role="tab">
                    <i class="fas fa-clock me-2"></i>
                    الحضور والإجازات
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="employeeTabsContent">
            <!-- Personal Information Tab -->
            <div class="tab-pane fade show active" id="personal" role="tabpanel">
                <div class="card border-top-0">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الاسم الأول</label>
                                <p class="fw-bold">{{ employee.first_name }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الاسم الأخير</label>
                                <p class="fw-bold">{{ employee.last_name }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الاسم الكامل</label>
                                <p class="fw-bold">{{ employee.full_name }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الجنسية</label>
                                <p class="fw-bold">{{ employee.nationality|default:"غير محدد" }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الرقم القومي</label>
                                <p class="fw-bold">{{ employee.national_id|default:"غير محدد" }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">تاريخ الميلاد</label>
                                <p class="fw-bold">{{ employee.date_of_birth|default:"غير محدد" }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الجنس</label>
                                <p class="fw-bold">{{ employee.get_gender_display|default:"غير محدد" }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الحالة الاجتماعية</label>
                                <p class="fw-bold">{{ employee.get_marital_status_display|default:"غير محدد" }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">البريد الإلكتروني</label>
                                <p class="fw-bold">
                                    <a href="mailto:{{ employee.email }}">{{ employee.email }}</a>
                                </p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الهاتف الأساسي</label>
                                <p class="fw-bold">
                                    <a href="tel:{{ employee.phone_primary }}">{{ employee.phone_primary }}</a>
                                </p>
                            </div>
                            {% if employee.phone_secondary %}
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الهاتف الثانوي</label>
                                <p class="fw-bold">
                                    <a href="tel:{{ employee.phone_secondary }}">{{ employee.phone_secondary }}</a>
                                </p>
                            </div>
                            {% endif %}
                            <div class="col-12 mb-3">
                                <label class="form-label text-muted">العنوان</label>
                                <p class="fw-bold">{{ employee.address|default:"غير محدد" }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Employment Information Tab -->
            <div class="tab-pane fade" id="employment" role="tabpanel">
                <div class="card border-top-0">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">رقم الموظف</label>
                                <p class="fw-bold">{{ employee.employee_number }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الشركة</label>
                                <p class="fw-bold">{{ employee.company.name }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الفرع</label>
                                <p class="fw-bold">{{ employee.branch.name|default:"غير محدد" }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">القسم</label>
                                <p class="fw-bold">{{ employee.department.name|default:"غير محدد" }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">المنصب الوظيفي</label>
                                <p class="fw-bold">{{ employee.job_position.title|default:"غير محدد" }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">المدير المباشر</label>
                                <p class="fw-bold">
                                    {% if employee.direct_manager %}
                                        <a href="{% url 'hr:new_employee_detail' employee.direct_manager.pk %}">
                                            {{ employee.direct_manager.full_name }}
                                        </a>
                                    {% else %}
                                        غير محدد
                                    {% endif %}
                                </p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">تاريخ التوظيف</label>
                                <p class="fw-bold">{{ employee.hire_date }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">نوع التوظيف</label>
                                <p class="fw-bold">{{ employee.get_employment_type_display }}</p>
                            </div>
                            {% if employee.contract_start_date %}
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">بداية العقد</label>
                                <p class="fw-bold">{{ employee.contract_start_date }}</p>
                            </div>
                            {% endif %}
                            {% if employee.contract_end_date %}
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">نهاية العقد</label>
                                <p class="fw-bold">
                                    {{ employee.contract_end_date }}
                                    {% now "Y-m-d" as today %}
                                    {% if employee.contract_end_date|date:"Y-m-d" < today %}
                                        <span class="badge bg-danger ms-2">منتهي</span>
                                    {% endif %}
                                </p>
                            </div>
                            {% endif %}
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الحالة الوظيفية</label>
                                <p class="fw-bold">
                                    <span class="badge bg-{% if employee.status == 'active' %}success{% elif employee.status == 'inactive' %}secondary{% elif employee.status == 'on_leave' %}warning{% else %}danger{% endif %}">
                                        {{ employee.get_status_display }}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Documents Tab -->
            <div class="tab-pane fade" id="documents" role="tabpanel">
                <div class="card border-top-0">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">الوثائق والمرفقات</h6>
                        <a href="{% url 'hr:new_employee_document_create' employee.pk %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i>
                            رفع وثيقة
                        </a>
                    </div>
                    <div class="card-body">
                        {% if documents %}
                            <div class="row">
                                {% for document in documents %}
                                <div class="col-md-6 mb-3">
                                    <div class="card border">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="fas fa-file-alt fa-2x text-primary"></i>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1">{{ document.document_type }}</h6>
                                                    <small class="text-muted">{{ document.created_at|date:"d/m/Y" }}</small>
                                                    {% if document.expiry_date %}
                                                        <br>
                                                        <small class="text-warning">
                                                            <i class="fas fa-calendar-times"></i>
                                                            ينتهي: {{ document.expiry_date }}
                                                        </small>
                                                    {% endif %}
                                                </div>
                                                <div>
                                                    <a href="{{ document.file.url }}" target="_blank" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% if documents.count > 4 %}
                            <div class="text-center">
                                <a href="{% url 'hr:new_employee_documents' employee.pk %}" class="btn btn-outline-primary">
                                    عرض جميع الوثائق ({{ documents.count }})
                                </a>
                            </div>
                            {% endif %}
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">لا توجد وثائق مرفوعة</h6>
                                <a href="{% url 'hr:new_employee_document_create' employee.pk %}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i>
                                    رفع أول وثيقة
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Emergency Contacts Tab -->
            <div class="tab-pane fade" id="contacts" role="tabpanel">
                <div class="card border-top-0">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">جهات الاتصال في الطوارئ</h6>
                        <a href="{% url 'hr:new_employee_emergency_contact_create' employee.pk %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i>
                            إضافة جهة اتصال
                        </a>
                    </div>
                    <div class="card-body">
                        {% if emergency_contacts %}
                            {% for contact in emergency_contacts %}
                            <div class="card border mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">
                                                {{ contact.name }}
                                                {% if contact.is_primary %}
                                                    <span class="badge bg-primary ms-2">أساسي</span>
                                                {% endif %}
                                            </h6>
                                            <p class="text-muted mb-1">{{ contact.relationship }}</p>
                                            <p class="mb-0">
                                                <i class="fas fa-phone me-2"></i>
                                                <a href="tel:{{ contact.phone }}">{{ contact.phone }}</a>
                                            </p>
                                            {% if contact.email %}
                                            <p class="mb-0">
                                                <i class="fas fa-envelope me-2"></i>
                                                <a href="mailto:{{ contact.email }}">{{ contact.email }}</a>
                                            </p>
                                            {% endif %}
                                        </div>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-phone fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">لا توجد جهات اتصال مضافة</h6>
                                <a href="{% url 'hr:new_employee_emergency_contact_create' employee.pk %}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i>
                                    إضافة جهة اتصال
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Attendance and Leave Tab -->
            <div class="tab-pane fade" id="attendance" role="tabpanel">
                <div class="card border-top-0">
                    <div class="card-body">
                        <div class="row">
                            <!-- Current Leave Requests -->
                            <div class="col-md-6 mb-4">
                                <h6 class="mb-3">
                                    <i class="fas fa-calendar-times me-2"></i>
                                    الإجازات الحالية
                                </h6>
                                {% if current_leave_requests %}
                                    {% for leave in current_leave_requests %}
                                    <div class="card border-warning mb-2">
                                        <div class="card-body py-2">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <strong>{{ leave.leave_type.name }}</strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        {{ leave.start_date }} - {{ leave.end_date }}
                                                    </small>
                                                </div>
                                                <span class="badge bg-warning">{{ leave.get_status_display }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <p class="text-muted">لا توجد إجازات حالية</p>
                                {% endif %}
                            </div>

                            <!-- Recent Attendance -->
                            <div class="col-md-6 mb-4">
                                <h6 class="mb-3">
                                    <i class="fas fa-clock me-2"></i>
                                    الحضور الأخير
                                </h6>
                                <div class="text-center">
                                    <h4 class="text-success">{{ recent_attendance }}</h4>
                                    <small class="text-muted">أيام الحضور (آخر 30 يوم)</small>
                                </div>
                            </div>
                        </div>

                        <!-- Performance Evaluation -->
                        {% if latest_evaluation %}
                        <div class="row">
                            <div class="col-12">
                                <h6 class="mb-3">
                                    <i class="fas fa-star me-2"></i>
                                    آخر تقييم أداء
                                </h6>
                                <div class="card border-info">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>{{ latest_evaluation.evaluation_period }}</strong>
                                                <br>
                                                <small class="text-muted">{{ latest_evaluation.evaluation_date }}</small>
                                            </div>
                                            <div class="text-center">
                                                <h5 class="text-info mb-0">{{ latest_evaluation.overall_score }}/100</h5>
                                                <small class="text-muted">النتيجة الإجمالية</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusUpdateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-toggle-on me-2"></i>
                    تغيير حالة الموظف
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="statusUpdateForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label class="form-label">الحالة الحالية:</label>
                        <p class="fw-bold">{{ employee.get_status_display }}</p>
                    </div>
                    <div class="mb-3">
                        <label for="newStatus" class="form-label">الحالة الجديدة:</label>
                        <select class="form-select" id="newStatus" name="status" required>
                            {% for value, label in status_choices %}
                                <option value="{{ value }}" {% if value == employee.status %}selected{% endif %}>
                                    {{ label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم تسجيل هذا التغيير في سجل الموظف.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitStatusUpdate()">حفظ التغيير</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateEmployeeStatus() {
    $('#statusUpdateModal').modal('show');
}

function submitStatusUpdate() {
    const newStatus = $('#newStatus').val();
    
    $.post('{% url "hr:new_employee_status_update" employee.pk %}', {
        status: newStatus,
        csrfmiddlewaretoken: '{{ csrf_token }}'
    }, function(response) {
        if (response.success) {
            location.reload();
        } else {
            alert('حدث خطأ: ' + response.error);
        }
    }).fail(function() {
        alert('حدث خطأ في الاتصال');
    });
}

// Auto-load tab content based on URL hash
$(document).ready(function() {
    if (window.location.hash) {
        const tabId = window.location.hash.substring(1);
        const tabButton = $(`#${tabId}-tab`);
        if (tabButton.length) {
            tabButton.click();
        }
    }
});

// Update URL hash when tab changes
$('button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
    const tabId = e.target.getAttribute('data-bs-target').substring(1);
    window.location.hash = tabId;
});
</script>
{% endblock %}
