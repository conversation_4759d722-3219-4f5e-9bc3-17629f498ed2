{% extends 'Hr/base.html' %}
{% load static %}

{% block title %}طلبات الإجازات - نظام الموارد البشرية - ElDawliya{% endblock %}

{% block page_title %}
    <i class="fas fa-calendar-times me-2"></i>
    إدارة طلبات الإجازات
{% endblock %}

{% block header_actions %}
    <div class="btn-group" role="group">
        <a href="{% url 'hr:new_leave_request_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            طلب إجازة جديد
        </a>
        <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#bulkApprovalModal">
            <i class="fas fa-check-double"></i>
            موافقة مجمعة
        </button>
        <a href="{% url 'hr:new_leave_type_list' %}" class="btn btn-outline-info">
            <i class="fas fa-tags"></i>
            أنواع الإجازات
        </a>
    </div>
{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card border-start border-primary border-4">
            <div class="stats-number text-primary">{{ total_requests }}</div>
            <div class="stats-label">إجمالي الطلبات</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card border-start border-warning border-4">
            <div class="stats-number text-warning">{{ pending_requests }}</div>
            <div class="stats-label">في الانتظار</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card border-start border-success border-4">
            <div class="stats-number text-success">{{ approved_requests }}</div>
            <div class="stats-label">موافق عليها</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card border-start border-danger border-4">
            <div class="stats-number text-danger">{{ rejected_requests }}</div>
            <div class="stats-label">مرفوضة</div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            البحث والتصفية
        </h5>
    </div>
    <div class="card-body">
        <form method="get" id="filterForm">
            <div class="row g-3">
                <!-- Employee Filter -->
                <div class="col-lg-3 col-md-6">
                    <label for="employee" class="form-label">الموظف</label>
                    <select class="form-select" id="employee" name="employee">
                        <option value="">جميع الموظفين</option>
                        {% for emp in employees %}
                            <option value="{{ emp.id }}" {% if employee_filter == emp.id|stringformat:"s" %}selected{% endif %}>
                                {{ emp.full_name }} ({{ emp.employee_number }})
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Leave Type Filter -->
                <div class="col-lg-3 col-md-6">
                    <label for="leave_type" class="form-label">نوع الإجازة</label>
                    <select class="form-select" id="leave_type" name="leave_type">
                        <option value="">جميع الأنواع</option>
                        {% for type in leave_types %}
                            <option value="{{ type.id }}" {% if leave_type_filter == type.id|stringformat:"s" %}selected{% endif %}>
                                {{ type.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Status Filter -->
                <div class="col-lg-3 col-md-6">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Date Range -->
                <div class="col-lg-3 col-md-6">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>

                <div class="col-lg-3 col-md-6">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                        <i class="fas fa-eraser"></i>
                        مسح الفلاتر
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="setPendingFilter()">
                        <i class="fas fa-clock"></i>
                        المعلقة فقط
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Leave Requests Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            طلبات الإجازات ({{ leave_requests|length }} من {{ page_obj.paginator.count }})
        </h5>
        <div class="form-check">
            <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
            <label class="form-check-label" for="selectAll">
                تحديد الكل
            </label>
        </div>
    </div>
    <div class="card-body p-0">
        {% if leave_requests %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th width="50">
                                <input type="checkbox" class="form-check-input" id="selectAllHeader" onchange="toggleSelectAll()">
                            </th>
                            <th>الموظف</th>
                            <th>نوع الإجازة</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>عدد الأيام</th>
                            <th>الحالة</th>
                            <th>تاريخ التقديم</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for request in leave_requests %}
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input request-checkbox" 
                                       value="{{ request.id }}" name="selected_requests">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if request.employee.photo %}
                                        <img src="{{ request.employee.photo.url }}" class="rounded-circle me-2" width="32" height="32">
                                    {% else %}
                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" 
                                             style="width: 32px; height: 32px;">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    {% endif %}
                                    <div>
                                        <strong>{{ request.employee.full_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ request.employee.employee_number }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ request.leave_type.name }}</span>
                                {% if not request.leave_type.is_paid %}
                                    <br><small class="text-muted">بدون راتب</small>
                                {% endif %}
                            </td>
                            <td>
                                <strong>{{ request.start_date }}</strong>
                                <br>
                                <small class="text-muted">{{ request.start_date|date:"l" }}</small>
                            </td>
                            <td>
                                <strong>{{ request.end_date }}</strong>
                                <br>
                                <small class="text-muted">{{ request.end_date|date:"l" }}</small>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ request.total_days }} يوم</span>
                            </td>
                            <td>
                                <span class="badge bg-{% if request.status == 'submitted' %}warning{% elif request.status == 'approved' %}success{% elif request.status == 'rejected' %}danger{% else %}secondary{% endif %}">
                                    {{ request.get_status_display }}
                                </span>
                                {% if request.approved_by %}
                                    <br>
                                    <small class="text-muted">{{ request.approved_by.get_full_name }}</small>
                                {% endif %}
                            </td>
                            <td>
                                {{ request.created_at|date:"d/m/Y" }}
                                <br>
                                <small class="text-muted">{{ request.created_at|time:"H:i" }}</small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{% url 'hr:new_leave_request_detail' request.pk %}" 
                                       class="btn btn-outline-primary" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    
                                    {% if request.status == 'submitted' %}
                                        <button type="button" class="btn btn-outline-success" 
                                                onclick="approveRequest({{ request.id }})" title="موافقة">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="rejectRequest({{ request.id }})" title="رفض">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    {% endif %}
                                    
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" 
                                                data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <a class="dropdown-item" href="#">
                                                    <i class="fas fa-edit me-2"></i>
                                                    تعديل
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="#">
                                                    <i class="fas fa-print me-2"></i>
                                                    طباعة
                                                </a>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <button class="dropdown-item text-danger" 
                                                        onclick="deleteRequest({{ request.id }})">
                                                    <i class="fas fa-trash me-2"></i>
                                                    حذف
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <div class="card-footer">
                <nav aria-label="تنقل الصفحات">
                    <ul class="pagination justify-content-center mb-0">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page=1">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.paginator.num_pages }}">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد طلبات إجازات</h5>
                <p class="text-muted">لم يتم العثور على طلبات إجازات مطابقة لمعايير البحث</p>
                <a href="{% url 'hr:new_leave_request_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    تقديم أول طلب إجازة
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Bulk Approval Modal -->
<div class="modal fade" id="bulkApprovalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-check-double me-2"></i>
                    الموافقة المجمعة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="bulkApprovalForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label class="form-label">اختر الإجراء:</label>
                        <select class="form-select" name="action" required>
                            <option value="">-- اختر الإجراء --</option>
                            <option value="approve">موافقة على الطلبات المحددة</option>
                            <option value="reject">رفض الطلبات المحددة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="bulkNotes" class="form-label">ملاحظات (اختياري)</label>
                        <textarea class="form-control" id="bulkNotes" name="notes" rows="3" 
                                  placeholder="أدخل ملاحظات حول القرار..."></textarea>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم تطبيق الإجراء على الطلبات المحددة فقط.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="executeBulkAction()">تنفيذ الإجراء</button>
            </div>
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-check me-2"></i>
                    موافقة على طلب الإجازة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="approvalForm">
                    {% csrf_token %}
                    <input type="hidden" id="requestId" name="request_id">
                    <div class="mb-3">
                        <label for="approvalNotes" class="form-label">ملاحظات الموافقة</label>
                        <textarea class="form-control" id="approvalNotes" name="notes" rows="3" 
                                  placeholder="أدخل ملاحظات حول الموافقة..."></textarea>
                    </div>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        سيتم إرسال إشعار للموظف بالموافقة على طلب الإجازة.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="submitApproval()">
                    <i class="fas fa-check"></i>
                    موافقة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div class="modal fade" id="rejectionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-times me-2"></i>
                    رفض طلب الإجازة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="rejectionForm">
                    {% csrf_token %}
                    <input type="hidden" id="rejectionRequestId" name="request_id">
                    <div class="mb-3">
                        <label for="rejectionNotes" class="form-label required">سبب الرفض</label>
                        <textarea class="form-control" id="rejectionNotes" name="notes" rows="3" 
                                  placeholder="أدخل سبب رفض طلب الإجازة..." required></textarea>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        سيتم إرسال إشعار للموظف برفض طلب الإجازة مع السبب المذكور.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" onclick="submitRejection()">
                    <i class="fas fa-times"></i>
                    رفض
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Filter functions
function clearFilters() {
    $('#filterForm')[0].reset();
    window.location.href = '{% url "hr:new_leave_request_list" %}';
}

function setPendingFilter() {
    $('#status').val('submitted');
    $('#filterForm').submit();
}

// Selection functions
function toggleSelectAll() {
    const selectAll = $('#selectAll').is(':checked') || $('#selectAllHeader').is(':checked');
    $('.request-checkbox').prop('checked', selectAll);
    updateBulkActionsButton();
}

function updateBulkActionsButton() {
    const selectedCount = $('.request-checkbox:checked').length;
    const bulkButton = $('[data-bs-target="#bulkApprovalModal"]');
    
    if (selectedCount > 0) {
        bulkButton.removeClass('btn-outline-warning').addClass('btn-warning');
        bulkButton.html(`<i class="fas fa-check-double"></i> موافقة مجمعة (${selectedCount})`);
    } else {
        bulkButton.removeClass('btn-warning').addClass('btn-outline-warning');
        bulkButton.html('<i class="fas fa-check-double"></i> موافقة مجمعة');
    }
}

// Update selection count when checkboxes change
$(document).on('change', '.request-checkbox', function() {
    updateBulkActionsButton();
});

// Approval functions
function approveRequest(requestId) {
    $('#requestId').val(requestId);
    $('#approvalModal').modal('show');
}

function submitApproval() {
    const requestId = $('#requestId').val();
    const notes = $('#approvalNotes').val();
    
    $.post(`/hr/leave/request/${requestId}/approve/`, {
        notes: notes,
        csrfmiddlewaretoken: '{{ csrf_token }}'
    }, function(response) {
        if (response.success) {
            $('#approvalModal').modal('hide');
            showAlert('تم الموافقة على طلب الإجازة بنجاح', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert(response.error, 'danger');
        }
    }).fail(function() {
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
}

// Rejection functions
function rejectRequest(requestId) {
    $('#rejectionRequestId').val(requestId);
    $('#rejectionModal').modal('show');
}

function submitRejection() {
    const requestId = $('#rejectionRequestId').val();
    const notes = $('#rejectionNotes').val();
    
    if (!notes.trim()) {
        showAlert('يرجى إدخال سبب الرفض', 'warning');
        return;
    }
    
    $.post(`/hr/leave/request/${requestId}/reject/`, {
        notes: notes,
        csrfmiddlewaretoken: '{{ csrf_token }}'
    }, function(response) {
        if (response.success) {
            $('#rejectionModal').modal('hide');
            showAlert('تم رفض طلب الإجازة', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert(response.error, 'danger');
        }
    }).fail(function() {
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
}

// Bulk actions
function executeBulkAction() {
    const selectedRequests = $('.request-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedRequests.length === 0) {
        showAlert('يرجى اختيار طلب واحد على الأقل', 'warning');
        return;
    }
    
    const action = $('#bulkApprovalForm select[name="action"]').val();
    const notes = $('#bulkNotes').val();
    
    if (!action) {
        showAlert('يرجى اختيار الإجراء', 'warning');
        return;
    }
    
    $.post('/hr/leave/requests/bulk-action/', {
        action: action,
        notes: notes,
        request_ids: selectedRequests,
        csrfmiddlewaretoken: '{{ csrf_token }}'
    }, function(response) {
        if (response.success) {
            $('#bulkApprovalModal').modal('hide');
            showAlert(`تم تنفيذ الإجراء على ${selectedRequests.length} طلب`, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert(response.error, 'danger');
        }
    }).fail(function() {
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
}

// Delete request
function deleteRequest(requestId) {
    if (confirm('هل أنت متأكد من حذف هذا الطلب؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه.')) {
        $.post(`/hr/leave/request/${requestId}/delete/`, {
            csrfmiddlewaretoken: '{{ csrf_token }}'
        }, function(response) {
            if (response.success) {
                showAlert('تم حذف الطلب بنجاح', 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('حدث خطأ في حذف الطلب', 'danger');
            }
        });
    }
}

// Show alert
function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.main-content').prepend(alertHtml);
    
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
{% endblock %}
