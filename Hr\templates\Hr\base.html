<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    <title>{% block title %}نظام الموارد البشرية - ElDawliya{% endblock %}</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <style>
        :root {
            --eldawliya-primary: #1e3a8a;
            --eldawliya-secondary: #3b82f6;
            --eldawliya-success: #10b981;
            --eldawliya-warning: #f59e0b;
            --eldawliya-danger: #ef4444;
            --eldawliya-info: #06b6d4;
            --eldawliya-light: #f8fafc;
            --eldawliya-dark: #1e293b;
            --eldawliya-sidebar: #1e293b;
            --eldawliya-sidebar-hover: #334155;
        }

        * {
            font-family: 'Cairo', sans-serif;
        }

        body {
            background-color: var(--eldawliya-light);
            font-size: 14px;
            line-height: 1.6;
        }

        /* Sidebar Styles */
        .sidebar {
            background: linear-gradient(135deg, var(--eldawliya-sidebar) 0%, var(--eldawliya-primary) 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: var(--eldawliya-sidebar-hover);
            border-right-color: var(--eldawliya-secondary);
            transform: translateX(-2px);
        }

        .sidebar .nav-link i {
            width: 20px;
            margin-left: 10px;
        }

        /* Header Styles */
        .main-header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-bottom: 3px solid var(--eldawliya-primary);
        }

        .navbar-brand {
            color: var(--eldawliya-primary) !important;
            font-weight: 700;
            font-size: 1.5rem;
        }

        /* Content Area */
        .main-content {
            padding: 20px;
            min-height: calc(100vh - 80px);
        }

        /* Cards */
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, var(--eldawliya-primary) 0%, var(--eldawliya-secondary) 100%);
            color: white;
            border-radius: 12px 12px 0 0 !important;
            padding: 15px 20px;
            font-weight: 600;
        }

        /* Buttons */
        .btn {
            border-radius: 8px;
            padding: 8px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--eldawliya-primary) 0%, var(--eldawliya-secondary) 100%);
            border: none;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
        }

        /* Tables */
        .table {
            border-radius: 8px;
            overflow: hidden;
        }

        .table thead th {
            background-color: var(--eldawliya-primary);
            color: white;
            border: none;
            font-weight: 600;
            padding: 15px;
        }

        .table tbody tr:hover {
            background-color: rgba(59, 130, 246, 0.05);
        }

        /* Forms */
        .form-control,
        .form-select {
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            padding: 10px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus,
        .form-select:focus {
            border-color: var(--eldawliya-secondary);
            box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        }

        /* Alerts */
        .alert {
            border-radius: 8px;
            border: none;
            padding: 15px 20px;
        }

        /* Stats Cards */
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            border-top: 4px solid var(--eldawliya-primary);
        }

        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .stats-card .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--eldawliya-primary);
            margin-bottom: 5px;
        }

        .stats-card .stats-label {
            color: #64748b;
            font-weight: 500;
        }

        /* Loading Spinner */
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 20px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                right: -250px;
                width: 250px;
                z-index: 1050;
                transition: right 0.3s ease;
            }

            .sidebar.show {
                right: 0;
            }

            .main-content {
                margin-right: 0;
            }
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--eldawliya-secondary);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--eldawliya-primary);
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in-right {
            animation: slideInRight 0.5s ease-out;
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(30px); }
            to { opacity: 1; transform: translateX(0); }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Main Container -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" id="sidebarMenu">
                <div class="position-sticky pt-3">
                    <!-- Logo -->
                    <div class="text-center mb-4">
                        <h4 class="text-white fw-bold">
                            <i class="fas fa-building me-2"></i>
                            ElDawliya HR
                        </h4>
                        <small class="text-white-50">نظام الموارد البشرية</small>
                    </div>

                    <!-- Navigation Menu -->
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'hr_dashboard' %}active{% endif %}" 
                               href="{% url 'hr:hr_dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i>
                                لوحة التحكم
                            </a>
                        </li>

                        <!-- Company Structure -->
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="collapse" href="#companyMenu" role="button">
                                <i class="fas fa-sitemap"></i>
                                هيكل الشركة
                                <i class="fas fa-chevron-down float-start mt-1"></i>
                            </a>
                            <div class="collapse" id="companyMenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-building"></i>
                                            الشركات
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-code-branch"></i>
                                            الفروع
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-users-cog"></i>
                                            الأقسام
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-user-tie"></i>
                                            المناصب
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Employee Management -->
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="collapse" href="#employeeMenu" role="button">
                                <i class="fas fa-users"></i>
                                إدارة الموظفين
                                <i class="fas fa-chevron-down float-start mt-1"></i>
                            </a>
                            <div class="collapse" id="employeeMenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-list"></i>
                                            قائمة الموظفين
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-user-plus"></i>
                                            إضافة موظف
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Attendance -->
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="collapse" href="#attendanceMenu" role="button">
                                <i class="fas fa-clock"></i>
                                الحضور والانصراف
                                <i class="fas fa-chevron-down float-start mt-1"></i>
                            </a>
                            <div class="collapse" id="attendanceMenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-list-alt"></i>
                                            سجلات الحضور
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-business-time"></i>
                                            الورديات
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-chart-line"></i>
                                            تقارير الحضور
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Leave Management -->
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="collapse" href="#leaveMenu" role="button">
                                <i class="fas fa-calendar-times"></i>
                                إدارة الإجازات
                                <i class="fas fa-chevron-down float-start mt-1"></i>
                            </a>
                            <div class="collapse" id="leaveMenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-calendar-check"></i>
                                            طلبات الإجازات
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-tags"></i>
                                            أنواع الإجازات
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-balance-scale"></i>
                                            أرصدة الإجازات
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <!-- Payroll -->
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="collapse" href="#payrollMenu" role="button">
                                <i class="fas fa-money-bill-wave"></i>
                                نظام الرواتب
                                <i class="fas fa-chevron-down float-start mt-1"></i>
                            </a>
                            <div class="collapse" id="payrollMenu">
                                <ul class="nav flex-column ms-3">
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-receipt"></i>
                                            سجلات الرواتب
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-calendar-alt"></i>
                                            فترات الرواتب
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#">
                                            <i class="fas fa-puzzle-piece"></i>
                                            مكونات الراتب
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                    </ul>

                    <!-- User Info -->
                    <div class="mt-auto pt-4 border-top border-secondary">
                        <div class="text-center text-white">
                            <div class="mb-2">
                                <i class="fas fa-user-circle fa-2x"></i>
                            </div>
                            <small>{{ user.get_full_name|default:user.username }}</small>
                            <div class="mt-2">
                                <a href="{% url 'admin:logout' %}" class="btn btn-outline-light btn-sm">
                                    <i class="fas fa-sign-out-alt"></i>
                                    تسجيل الخروج
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- Header -->
                <header class="main-header d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
                    <div class="d-flex align-items-center">
                        <button class="btn btn-outline-primary d-md-none me-2" type="button" data-bs-toggle="collapse" 
                                data-bs-target="#sidebarMenu" aria-controls="sidebarMenu">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h1 class="h2 mb-0">{% block page_title %}لوحة التحكم{% endblock %}</h1>
                    </div>
                    
                    <div class="btn-toolbar mb-2 mb-md-0">
                        {% block header_actions %}{% endblock %}
                    </div>
                </header>

                <!-- Messages -->
                {% if messages %}
                    <div class="messages-container">
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-circle{% elif message.tags == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Main Content Area -->
                <div class="main-content fade-in">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Auto-hide messages after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // Sidebar toggle for mobile
        $('.navbar-toggler').click(function() {
            $('.sidebar').toggleClass('show');
        });

        // Close sidebar when clicking outside on mobile
        $(document).click(function(e) {
            if ($(window).width() < 768) {
                if (!$(e.target).closest('.sidebar, .navbar-toggler').length) {
                    $('.sidebar').removeClass('show');
                }
            }
        });

        // Loading spinner function
        function showLoading() {
            $('.loading-spinner').show();
        }

        function hideLoading() {
            $('.loading-spinner').hide();
        }

        // CSRF Token for AJAX
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        const csrftoken = getCookie('csrftoken');

        // Setup AJAX with CSRF token
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!this.crossDomain) {
                    xhr.setRequestHeader("X-CSRFToken", csrftoken);
                }
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
